<template>
  <!-- 方案1: 使用条件渲染控制文字 -->
  <!-- <el-button type="success" :icon="Delete" loading :loading-icon="Eleme" round>
    {{ isLoading ? '加载中' : 'I am ElButton' }}
  </el-button> -->

  <!-- 方案2: 使用两个按钮切换 -->
  <!-- <el-button v-if="!isLoading" type="danger" :icon="Delete" round @click="startLoading">
    I am ElButton
  </el-button>
  <el-button v-else type="success" loading :loading-icon="Eleme" round> 加载中 </el-button> -->

  <!-- 测试按钮 -->
  <!-- <el-button @click="toggleLoading" style="margin-left: 10px"> 切换加载状态 </el-button> -->
  <!-- <div>
    <el-button-group>
      <el-button :icon="Edit">左侧按钮</el-button>
      <el-button :icon="Search">中间按钮</el-button>
      <el-button :icon="Upload">右侧按钮</el-button>
    </el-button-group>
  </div> -->

  <!-- <div class="common-layout">
    <el-container>
      <el-header>Header</el-header>
      <el-container>
        <el-aside width="200px">Aside</el-aside>
        <el-main>Main</el-main>
      </el-container>
      <el-footer>Footer</el-footer>
    </el-container>
  </div> -->

  <!-- <div style="font-size: 20px">
    <Edit style="width: 2em; height: 2em; margin-right: 16px" />
    <Share style="width: 2em; height: 2em; margin-right: 16px" />
    <Delete style="width: 2em; height: 2em; margin-right: 16px" />
    <Search style="width: 2em; height: 2em; margin-right: 16px" />
    <el-icon class="is-loading">
      <Loading />
    </el-icon>
  </div> -->
  <el-text type="primary" class="mx-1" size="large">I am ElText</el-text>
  <el-text type="success" class="mx-1" size="large">I am ElText</el-text>
  <el-text type="warning" class="mx-1" size="default">I am ElText</el-text>
  <el-text type="danger" class="mx-1" size="small">I am ElText</el-text>
  <el-text type="info" class="mx-1" size="small">I am ElText</el-text>
</template>

<script setup lang="ts">
// import { ElButton, ElButtonGroup } from 'element-plus'
// import { Delete, Edit, Search, Upload, Eleme } from '@element-plus/icons-vue'
// import { ElContainer, ElHeader, ElMain, ElFooter, ElIcon } from 'element-plus'
// import { Edit, Share, Delete, Loading, Search } from '@element-plus/icons-vue'
import { ElText } from 'element-plus'
// import { ref } from 'vue'

// const isLoading = ref(false)

// const toggleLoading = () => {
//     isLoading.value = !isLoading.value
//   },
//   startLoading = () => {
//     isLoading.value = true
//     setTimeout(() => {
//       isLoading.value = false
//     }, 3000)
//   }
</script>

<style scoped>
/* .el-button-group {
  margin-top: 20px;
} */

.mx-1 {
  margin-right: 16px;
}
</style>
