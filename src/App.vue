<template>
  <el-button type="success" :icon="Delete" loading :loading-icon="Eleme">
    <template #default>I am ElButton</template>
  </el-button>
  <div>
    <el-button-group>
      <el-button :icon="Edit">左侧按钮</el-button>
      <el-button :icon="Search">中间按钮</el-button>
      <el-button :icon="Upload">右侧按钮</el-button>
    </el-button-group>
  </div>
</template>

<script lang="ts">
import { ElButton, ElButtonGroup } from 'element-plus'
import { Delete, Edit, Search, Upload, Eleme } from '@element-plus/icons-vue'

export default {
  components: { ElButton, ElButtonGroup },
  data() {
    return {
      Delete,
      Edit,
      Search,
      Upload,
      Eleme,
    }
  },
}
</script>

<style scoped>
.el-button-group {
  margin-top: 20px;
}
</style>
