<template>
  <!-- 方案1: 使用条件渲染控制文字 -->
  <el-button type="success" :icon="Delete" loading :loading-icon="Eleme" round>
    {{ isLoading ? '加载中' : 'I am ElButton' }}
  </el-button>

  <!-- 方案2: 使用两个按钮切换 -->
  <el-button v-if="!isLoading" type="success" :icon="Delete" round @click="startLoading">
    I am ElButton
  </el-button>
  <el-button v-else type="success" loading :loading-icon="Eleme" round> 加载中 </el-button>

  <!-- 测试按钮 -->
  <el-button @click="toggleLoading" style="margin-left: 10px"> 切换加载状态 </el-button>
  <div>
    <el-button-group>
      <el-button :icon="Edit">左侧按钮</el-button>
      <el-button :icon="Search">中间按钮</el-button>
      <el-button :icon="Upload">右侧按钮</el-button>
    </el-button-group>
  </div>
</template>

<script setup lang="ts">
import { ElButton, ElButtonGroup } from 'element-plus'
import { Delete, Edit, Search, Upload, Eleme } from '@element-plus/icons-vue'
import { ref } from 'vue'
  const isLoading = ref(false)
  const toggleLoading = () => {
      isLoading.value = !isLoading.value
    },
    startLoading() {
      isLoading.value = true
      // 模拟异步操作，3秒后停止加载
      setTimeout(() => {
        isLoading.value = false
      }, 3000)
    },
  },
}
</script>

<style scoped>
.el-button-group {
  margin-top: 20px;
}
</style>
