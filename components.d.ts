/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElAside: typeof import('element-plus/es')['ElAside']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElText: typeof import('element-plus/es')['ElText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
